'use client';

import React, { useEffect, useRef, useState } from 'react';

interface GoogleMapProps {
  onLocationSelect: (location: { lat: number; lng: number }) => void;
  initialLocation?: { lat: number; lng: number } | null;
}

const GoogleMap: React.FC<GoogleMapProps> = ({ onLocationSelect, initialLocation = null }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const markerRef = useRef<any>(null);
  const mapInstanceRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<{ lat: number; lng: number }>(
    initialLocation || { lat: 10.3157, lng: 123.8854 }
  );

  // Default location (Cebu City)
  const DEFAULT_LOCATION = { lat: 10.3157, lng: 123.8854 };
  const ZOOM_LEVEL = 15;

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapRef.current) return;

    // Only initialize the map once
    if (mapInstanceRef.current) return;

    setIsLoading(true);

    const initializeMap = () => {
      try {
        // Check if Google Maps API is already loaded
        if (window.google && window.google.maps) {
          initMap();
          return;
        }

        // Check if script is already being loaded
        const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
        if (existingScript) {
          existingScript.addEventListener('load', initMap);
          existingScript.addEventListener('error', handleMapError);
          return;
        }

        // Load Google Maps API
        const script = document.createElement('script');
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        script.addEventListener('load', initMap);
        script.addEventListener('error', handleMapError);
        document.head.appendChild(script);
      } catch (error) {
        console.error('Error initializing map:', error);
        handleMapError();
      }
    };

    const handleMapError = () => {
      console.error('Failed to load Google Maps');
      setError('Failed to load map. Please check your internet connection.');
      setIsLoading(false);
    };

    const initMap = () => {
      if (!mapRef.current) {
        console.error('Map container not found');
        return;
      }

      try {
        console.log('Initializing Google Maps...');

        // Create map with clean styling
        const map = new window.google.maps.Map(mapRef.current!, {
          center: selectedLocation,
          zoom: ZOOM_LEVEL,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
          zoomControl: true,
          styles: [
            {
              featureType: "poi",
              elementType: "labels",
              stylers: [{ visibility: "off" }]
            }
          ]
        });

        mapInstanceRef.current = map;

        // Create marker with default red pin
        const marker = new window.google.maps.Marker({
          position: selectedLocation,
          map: map,
          draggable: true
        });

        markerRef.current = marker;

        // Add event listeners for marker drag
        marker.addListener('dragend', function() {
          const position = marker.getPosition();
          if (position) {
            const newLocation = {
              lat: position.lat(),
              lng: position.lng()
            };
            setSelectedLocation(newLocation);
            onLocationSelect(newLocation);
          }
        });

        // Add event listeners for map click
        map.addListener('click', function(event: any) {
          const clickedLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
          };
          marker.setPosition(clickedLocation);
          setSelectedLocation(clickedLocation);
          onLocationSelect(clickedLocation);
        });

        console.log('Google Maps initialized successfully');
        setIsLoading(false);
        setError(null);
      } catch (error) {
        console.error('Error creating map:', error);
        handleMapError();
      }
    };

    initializeMap();
  }, []);

  // Update location when initialLocation changes
  useEffect(() => {
    if (initialLocation && mapInstanceRef.current && markerRef.current) {
      setSelectedLocation(initialLocation);
      mapInstanceRef.current.setCenter(initialLocation);
      markerRef.current.setPosition(initialLocation);
    }
  }, [initialLocation]);

  if (error) {
    return (
      <div className="space-y-4">
        <div className="h-64 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center justify-center">
          <div className="text-center p-4">
            <i className="fas fa-map-marker-alt text-yellow-600 text-3xl mb-3"></i>
            <p className="text-yellow-800 font-medium mb-2">Map temporarily unavailable</p>
            <p className="text-sm text-yellow-700 mb-3">We'll use Cebu City as your default location</p>
            <p className="text-xs text-yellow-600">You can update your exact location later in your dashboard</p>
          </div>
        </div>

        {/* Manual Location Input */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-3">
            <i className="fas fa-edit mr-2"></i>
            Or enter your location manually:
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-blue-700 mb-1">Latitude</label>
              <input
                type="number"
                step="any"
                value={selectedLocation.lat}
                onChange={(e) => {
                  const newLat = parseFloat(e.target.value) || 10.3157;
                  const newLocation = { lat: newLat, lng: selectedLocation.lng };
                  setSelectedLocation(newLocation);
                  onLocationSelect(newLocation);
                }}
                className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="10.3157"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-blue-700 mb-1">Longitude</label>
              <input
                type="number"
                step="any"
                value={selectedLocation.lng}
                onChange={(e) => {
                  const newLng = parseFloat(e.target.value) || 123.8854;
                  const newLocation = { lat: selectedLocation.lat, lng: newLng };
                  setSelectedLocation(newLocation);
                  onLocationSelect(newLocation);
                }}
                className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="123.8854"
              />
            </div>
          </div>
        </div>

        {/* Current Selection Display */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <i className="fas fa-check-circle text-green-600"></i>
            <div>
              <p className="text-sm font-medium text-green-800">Selected Location:</p>
              <p className="text-xs text-green-700">
                Latitude: {selectedLocation.lat.toFixed(6)}, Longitude: {selectedLocation.lng.toFixed(6)}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-64 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
          <p className="text-blue-700 font-medium">Loading Google Maps...</p>
          <p className="text-sm text-blue-600 mt-1">Setting up your location picker</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div
        ref={mapRef}
        className="h-64 w-full rounded-lg border-2 border-gray-300 bg-gray-100"
        style={{ minHeight: '256px' }}
      ></div>
      <div className="bg-blue-50 border border-blue-200 rounded p-3">
        <div className="flex items-start space-x-2">
          <i className="fas fa-map-marked-alt text-blue-600 mt-0.5"></i>
          <div>
            <p className="text-sm text-blue-800 font-medium">
              <i className="fas fa-map mr-1"></i>
              Google Maps - Interactive Location Picker
            </p>
            <ul className="text-xs text-blue-700 mt-1 space-y-1">
              <li>• Click anywhere on the map to place the red marker</li>
              <li>• Drag the red marker to fine-tune your exact location</li>
              <li>• Zoom in/out using the + and - buttons</li>
              <li>• The marker shows where your barbershop will appear to clients</li>
            </ul>
          </div>
        </div>
      </div>
      {selectedLocation && (
        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
          <div className="flex items-center justify-between">
            <div>
              <strong>Selected coordinates:</strong> {selectedLocation.lat.toFixed(6)}, {selectedLocation.lng.toFixed(6)}
            </div>
            <div className="text-green-600">
              <i className="fas fa-check-circle mr-1"></i>
              Location set
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoogleMap;
