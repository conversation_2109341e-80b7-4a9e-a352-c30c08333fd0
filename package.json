{"name": "alot", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint ."}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@headlessui/react": "^2.2.2", "chart.js": "^4.4.9", "firebase": "^10.7.0", "firebase-admin": "^13.3.0", "geofire-common": "^6.0.0", "next": "^15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-firebase-hooks": "^5.1.1", "tailwindcss": "^3.3.5"}, "devDependencies": {"@types/google.maps": "^3.58.1", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "typescript": "^5.3.2"}}